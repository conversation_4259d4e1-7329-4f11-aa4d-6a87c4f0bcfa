<?xml version="1.0" encoding="UTF-8"?>
<svg width="128" height="128" viewBox="0 0 128 128" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="dbGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#2ECC71;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#27AE60;stop-opacity:1" />
    </linearGradient>
    <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
      <feDropShadow dx="2" dy="4" stdDeviation="3" flood-color="#000000" flood-opacity="0.3"/>
    </filter>
  </defs>
  
  <!-- Background -->
  <rect x="8" y="8" width="112" height="112" rx="16" ry="16" fill="url(#dbGradient)" filter="url(#shadow)"/>
  
  <!-- Database cylinders -->
  <ellipse cx="64" cy="35" rx="28" ry="8" fill="#FFFFFF" opacity="0.9"/>
  <rect x="36" y="35" width="56" height="16" fill="#FFFFFF" opacity="0.9"/>
  <ellipse cx="64" cy="51" rx="28" ry="8" fill="#FFFFFF" opacity="0.7"/>
  
  <ellipse cx="64" cy="60" rx="28" ry="8" fill="#FFFFFF" opacity="0.9"/>
  <rect x="36" y="60" width="56" height="16" fill="#FFFFFF" opacity="0.9"/>
  <ellipse cx="64" cy="76" rx="28" ry="8" fill="#FFFFFF" opacity="0.7"/>
  
  <ellipse cx="64" cy="85" rx="28" ry="8" fill="#FFFFFF" opacity="0.9"/>
  <rect x="36" y="85" width="56" height="8" fill="#FFFFFF" opacity="0.9"/>
  <ellipse cx="64" cy="93" rx="28" ry="8" fill="#FFFFFF" opacity="0.7"/>
  
  <!-- Connection lines -->
  <circle cx="48" cy="45" r="2" fill="#27AE60"/>
  <circle cx="64" cy="45" r="2" fill="#27AE60"/>
  <circle cx="80" cy="45" r="2" fill="#27AE60"/>
</svg>
