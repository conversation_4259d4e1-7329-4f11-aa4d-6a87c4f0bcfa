<?xml version="1.0" encoding="UTF-8"?>
<svg width="128" height="128" viewBox="0 0 128 128" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="textGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#1ABC9C;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#16A085;stop-opacity:1" />
    </linearGradient>
    <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
      <feDropShadow dx="2" dy="4" stdDeviation="3" flood-color="#000000" flood-opacity="0.3"/>
    </filter>
  </defs>
  
  <!-- Background -->
  <rect x="8" y="8" width="112" height="112" rx="16" ry="16" fill="url(#textGradient)" filter="url(#shadow)"/>
  
  <!-- Document -->
  <rect x="28" y="24" width="72" height="80" rx="4" ry="4" fill="#FFFFFF" opacity="0.95"/>
  
  <!-- Document corner fold -->
  <path d="M88 24 L88 36 L100 36 Z" fill="#ECF0F1"/>
  <path d="M88 24 L88 36 L100 36 Z" fill="#BDC3C7" opacity="0.3"/>
  
  <!-- Text lines -->
  <rect x="36" y="36" width="48" height="3" rx="1.5" fill="#34495E" opacity="0.8"/>
  <rect x="36" y="44" width="52" height="3" rx="1.5" fill="#34495E" opacity="0.6"/>
  <rect x="36" y="52" width="44" height="3" rx="1.5" fill="#34495E" opacity="0.8"/>
  <rect x="36" y="60" width="56" height="3" rx="1.5" fill="#34495E" opacity="0.6"/>
  <rect x="36" y="68" width="40" height="3" rx="1.5" fill="#34495E" opacity="0.8"/>
  <rect x="36" y="76" width="48" height="3" rx="1.5" fill="#34495E" opacity="0.6"/>
  <rect x="36" y="84" width="32" height="3" rx="1.5" fill="#34495E" opacity="0.8"/>
  
  <!-- Pen/pencil -->
  <path d="M72 88 L76 84 L80 88 L76 92 Z" fill="#F39C12"/>
  <rect x="74" y="88" width="4" height="12" fill="#E67E22"/>
  <circle cx="76" cy="102" r="2" fill="#2C3E50"/>
  
  <!-- Writing indicator -->
  <circle cx="68" cy="84" r="1" fill="#E74C3C" opacity="0.7"/>
</svg>
