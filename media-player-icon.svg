<?xml version="1.0" encoding="UTF-8"?>
<svg width="128" height="128" viewBox="0 0 128 128" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="mediaGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#8E44AD;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#9B59B6;stop-opacity:1" />
    </linearGradient>
    <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
      <feDropShadow dx="2" dy="4" stdDeviation="3" flood-color="#000000" flood-opacity="0.3"/>
    </filter>
  </defs>
  
  <!-- Background -->
  <rect x="8" y="8" width="112" height="112" rx="16" ry="16" fill="url(#mediaGradient)" filter="url(#shadow)"/>
  
  <!-- Media player body -->
  <rect x="20" y="32" width="88" height="64" rx="8" ry="8" fill="#2C3E50" opacity="0.9"/>
  
  <!-- Screen -->
  <rect x="28" y="40" width="72" height="40" rx="4" ry="4" fill="#34495E"/>
  
  <!-- Play button (large) -->
  <circle cx="64" cy="60" r="16" fill="#FFFFFF" opacity="0.9"/>
  <path d="M58 52 L58 68 L74 60 Z" fill="#8E44AD"/>
  
  <!-- Control buttons -->
  <circle cx="40" cy="88" r="6" fill="#FFFFFF" opacity="0.8"/>
  <rect x="37" y="85" width="6" height="6" fill="#2C3E50"/>
  
  <circle cx="64" cy="88" r="6" fill="#FFFFFF" opacity="0.8"/>
  <path d="M61 85 L61 91 L67 88 Z" fill="#2C3E50"/>
  
  <circle cx="88" cy="88" r="6" fill="#FFFFFF" opacity="0.8"/>
  <rect x="85" y="85" width="2" height="6" fill="#2C3E50"/>
  <rect x="89" y="85" width="2" height="6" fill="#2C3E50"/>
  
  <!-- Volume indicator -->
  <path d="M24 48 L28 52 L28 68 L24 72" stroke="#FFFFFF" stroke-width="2" fill="none" opacity="0.7"/>
  <path d="M32 56 Q36 60 32 64" stroke="#FFFFFF" stroke-width="2" fill="none" opacity="0.5"/>
  <path d="M36 52 Q42 60 36 68" stroke="#FFFFFF" stroke-width="2" fill="none" opacity="0.3"/>
  
  <!-- Progress bar -->
  <rect x="28" y="82" width="72" height="2" rx="1" fill="#FFFFFF" opacity="0.3"/>
  <rect x="28" y="82" width="32" height="2" rx="1" fill="#1ABC9C"/>
  <circle cx="60" cy="83" r="3" fill="#1ABC9C"/>
</svg>
