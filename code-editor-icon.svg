<?xml version="1.0" encoding="UTF-8"?>
<svg width="128" height="128" viewBox="0 0 128 128" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="codeGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#4A90E2;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#357ABD;stop-opacity:1" />
    </linearGradient>
    <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
      <feDropShadow dx="2" dy="4" stdDeviation="3" flood-color="#000000" flood-opacity="0.3"/>
    </filter>
  </defs>
  
  <!-- Background -->
  <rect x="8" y="8" width="112" height="112" rx="16" ry="16" fill="url(#codeGradient)" filter="url(#shadow)"/>
  
  <!-- Code brackets -->
  <path d="M32 40 L24 48 L24 56 L32 64" stroke="#FFFFFF" stroke-width="3" fill="none" stroke-linecap="round" stroke-linejoin="round"/>
  <path d="M96 40 L104 48 L104 56 L96 64" stroke="#FFFFFF" stroke-width="3" fill="none" stroke-linecap="round" stroke-linejoin="round"/>
  
  <!-- Code lines -->
  <rect x="40" y="44" width="24" height="3" rx="1.5" fill="#FFFFFF" opacity="0.9"/>
  <rect x="40" y="52" width="32" height="3" rx="1.5" fill="#FFFFFF" opacity="0.7"/>
  <rect x="40" y="60" width="20" height="3" rx="1.5" fill="#FFFFFF" opacity="0.9"/>
  
  <!-- Cursor -->
  <rect x="68" y="44" width="2" height="20" fill="#FFD700"/>
</svg>
