<?xml version="1.0" encoding="UTF-8"?>
<svg width="128" height="128" viewBox="0 0 128 128" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="networkGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#9B59B6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#8E44AD;stop-opacity:1" />
    </linearGradient>
    <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
      <feDropShadow dx="2" dy="4" stdDeviation="3" flood-color="#000000" flood-opacity="0.3"/>
    </filter>
  </defs>
  
  <!-- Background -->
  <rect x="8" y="8" width="112" height="112" rx="16" ry="16" fill="url(#networkGradient)" filter="url(#shadow)"/>
  
  <!-- Network nodes -->
  <circle cx="40" cy="40" r="8" fill="#FFFFFF" opacity="0.9"/>
  <circle cx="88" cy="40" r="8" fill="#FFFFFF" opacity="0.9"/>
  <circle cx="64" cy="64" r="10" fill="#FFFFFF" opacity="0.9"/>
  <circle cx="40" cy="88" r="8" fill="#FFFFFF" opacity="0.9"/>
  <circle cx="88" cy="88" r="8" fill="#FFFFFF" opacity="0.9"/>
  
  <!-- Connection lines -->
  <line x1="40" y1="40" x2="64" y2="64" stroke="#FFFFFF" stroke-width="3" opacity="0.7"/>
  <line x1="88" y1="40" x2="64" y2="64" stroke="#FFFFFF" stroke-width="3" opacity="0.7"/>
  <line x1="64" y1="64" x2="40" y2="88" stroke="#FFFFFF" stroke-width="3" opacity="0.7"/>
  <line x1="64" y1="64" x2="88" y2="88" stroke="#FFFFFF" stroke-width="3" opacity="0.7"/>
  <line x1="40" y1="40" x2="88" y2="40" stroke="#FFFFFF" stroke-width="2" opacity="0.5"/>
  <line x1="40" y1="88" x2="88" y2="88" stroke="#FFFFFF" stroke-width="2" opacity="0.5"/>
  
  <!-- Data flow indicators -->
  <circle cx="52" cy="52" r="2" fill="#FFD700">
    <animate attributeName="opacity" values="0;1;0" dur="2s" repeatCount="indefinite"/>
  </circle>
  <circle cx="76" cy="52" r="2" fill="#FFD700">
    <animate attributeName="opacity" values="0;1;0" dur="2s" begin="0.5s" repeatCount="indefinite"/>
  </circle>
  <circle cx="52" cy="76" r="2" fill="#FFD700">
    <animate attributeName="opacity" values="0;1;0" dur="2s" begin="1s" repeatCount="indefinite"/>
  </circle>
</svg>
